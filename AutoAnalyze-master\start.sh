#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 打印彩色消息
print_colored() {
    echo -e "${2}${1}${NC}"
}

# 打印头部信息
print_header() {
    print_colored "================================================================" "$CYAN"
    print_colored "🚀 AutoAnalyze 快速启动脚本 (Linux/macOS)" "$BOLD$GREEN"
    print_colored "   AI驱动的数据分析工具" "$CYAN"
    print_colored "================================================================" "$CYAN"
    echo
}

# 检查命令是否存在
check_command() {
    if command -v "$1" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# 检查环境
check_environment() {
    print_colored "🔍 检查运行环境..." "$YELLOW"
    
    # 检查Python
    if check_command python3; then
        python_version=$(python3 --version 2>&1)
        print_colored "✅ Python: $python_version" "$GREEN"
    elif check_command python; then
        python_version=$(python --version 2>&1)
        print_colored "✅ Python: $python_version" "$GREEN"
    else
        print_colored "❌ Python 未安装或不在PATH中" "$RED"
        print_colored "请安装Python 3.10+: https://www.python.org/downloads/" "$RED"
        return 1
    fi
    
    # 检查Node.js
    if check_command node; then
        node_version=$(node --version 2>&1)
        print_colored "✅ Node.js: $node_version" "$GREEN"
    else
        print_colored "❌ Node.js 未安装或不在PATH中" "$RED"
        print_colored "请安装Node.js 16+: https://nodejs.org/" "$RED"
        return 1
    fi
    
    # 检查npm
    if check_command npm; then
        npm_version=$(npm --version 2>&1)
        print_colored "✅ npm: $npm_version" "$GREEN"
    else
        print_colored "❌ npm 未安装或不在PATH中" "$RED"
        return 1
    fi
    
    # 检查uv
    if check_command uv; then
        uv_version=$(uv --version 2>&1)
        print_colored "✅ uv: $uv_version" "$GREEN"
    else
        print_colored "⚠️  uv 未安装，正在尝试安装..." "$YELLOW"
        if command -v python3 &> /dev/null; then
            python3 -m pip install uv
        else
            python -m pip install uv
        fi
        
        if check_command uv; then
            print_colored "✅ uv 安装成功" "$GREEN"
        else
            print_colored "❌ uv 安装失败，请手动安装: pip install uv" "$RED"
            return 1
        fi
    fi
    
    echo
    return 0
}

# 检查端口
check_ports() {
    print_colored "🔌 检查端口可用性..." "$YELLOW"
    
    # 检查端口8000
    if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_colored "❌ 端口 8000 已被占用（后端服务端口）" "$RED"
        return 1
    else
        print_colored "✅ 端口 8000 可用" "$GREEN"
    fi
    
    # 检查端口5173
    if lsof -Pi :5173 -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_colored "❌ 端口 5173 已被占用（前端服务端口）" "$RED"
        return 1
    else
        print_colored "✅ 端口 5173 可用" "$GREEN"
    fi
    
    echo
    return 0
}

# 安装依赖
install_dependencies() {
    print_colored "📦 安装项目依赖..." "$YELLOW"
    
    # 安装后端依赖
    print_colored "  🔧 安装后端依赖..." "$CYAN"
    cd backend
    if uv sync; then
        print_colored "  ✅ 后端依赖安装完成" "$GREEN"
    else
        print_colored "  ❌ 后端依赖安装失败" "$RED"
        return 1
    fi
    
    # 注册IPython内核
    print_colored "  🔧 注册IPython内核..." "$CYAN"
    if source .venv/bin/activate && python -m ipykernel install --user >/dev/null 2>&1; then
        print_colored "  ✅ IPython内核注册成功" "$GREEN"
    else
        print_colored "  ⚠️  IPython内核注册失败，部分功能可能不可用" "$YELLOW"
    fi
    
    # 安装前端依赖
    print_colored "  🔧 安装前端依赖..." "$CYAN"
    cd ../frontend
    if npm install; then
        print_colored "  ✅ 前端依赖安装完成" "$GREEN"
    else
        print_colored "  ❌ 前端依赖安装失败" "$RED"
        return 1
    fi
    
    cd ..
    echo
    return 0
}

# 启动服务
start_services() {
    print_colored "🚀 启动服务..." "$YELLOW"
    
    # 启动后端服务
    print_colored "  启动后端服务..." "$CYAN"
    cd backend
    uv run app/main.py &
    BACKEND_PID=$!
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 启动前端服务
    print_colored "  启动前端服务..." "$CYAN"
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    # 等待服务启动
    print_colored "⏳ 等待服务启动完成..." "$YELLOW"
    sleep 5
    
    # 检查服务状态
    if curl -s http://127.0.0.1:8000 >/dev/null 2>&1; then
        print_colored "✅ 后端服务启动成功 (http://127.0.0.1:8000)" "$GREEN"
    else
        print_colored "⚠️  后端服务可能未完全启动，请稍等片刻" "$YELLOW"
    fi
    
    if curl -s http://localhost:5173 >/dev/null 2>&1; then
        print_colored "✅ 前端服务启动成功 (http://localhost:5173)" "$GREEN"
    else
        print_colored "⚠️  前端服务可能未完全启动，请稍等片刻" "$YELLOW"
    fi
    
    echo
    print_colored "🎉 AutoAnalyze 启动成功！" "$BOLD$GREEN"
    echo
    print_colored "================================================================" "$CYAN"
    print_colored "📋 服务信息:" "$BOLD"
    print_colored "   前端地址: http://localhost:5173" "$CYAN"
    print_colored "   后端地址: http://127.0.0.1:8000" "$CYAN"
    print_colored "   按 Ctrl+C 停止服务" "$YELLOW"
    print_colored "================================================================" "$CYAN"
    
    # 自动打开浏览器
    print_colored "🌐 正在打开浏览器..." "$CYAN"
    if command -v xdg-open &> /dev/null; then
        xdg-open http://localhost:5173 >/dev/null 2>&1
    elif command -v open &> /dev/null; then
        open http://localhost:5173 >/dev/null 2>&1
    else
        print_colored "⚠️  无法自动打开浏览器，请手动访问: http://localhost:5173" "$YELLOW"
    fi
    
    # 等待用户中断
    trap cleanup INT TERM
    
    while true; do
        sleep 1
    done
}

# 清理函数
cleanup() {
    echo
    print_colored "🛑 正在关闭服务..." "$YELLOW"
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        print_colored "✅ 后端服务已关闭" "$GREEN"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        print_colored "✅ 前端服务已关闭" "$GREEN"
    fi
    
    # 清理可能残留的进程
    pkill -f "uv run app/main.py" 2>/dev/null
    pkill -f "npm run dev" 2>/dev/null
    
    print_colored "👋 感谢使用 AutoAnalyze！" "$CYAN"
    exit 0
}

# 主函数
main() {
    print_header
    
    # 检查环境
    if ! check_environment; then
        print_colored "❌ 环境检查失败，请安装必需的软件" "$RED"
        exit 1
    fi
    
    # 检查端口（如果lsof可用）
    if command -v lsof &> /dev/null; then
        if ! check_ports; then
            print_colored "❌ 端口检查失败，请关闭占用端口的程序" "$RED"
            exit 1
        fi
    else
        print_colored "⚠️  lsof 未安装，跳过端口检查" "$YELLOW"
        echo
    fi
    
    # 安装依赖
    if ! install_dependencies; then
        print_colored "❌ 依赖安装失败" "$RED"
        exit 1
    fi
    
    # 启动服务
    start_services
}

# 检查是否在正确的目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    print_colored "❌ 请在 AutoAnalyze 项目根目录下运行此脚本" "$RED"
    exit 1
fi

# 运行主函数
main
