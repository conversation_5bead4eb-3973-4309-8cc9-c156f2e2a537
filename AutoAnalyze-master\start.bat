@echo off
chcp 65001 >nul
title AutoAnalyze 快速启动

echo ================================================================
echo 🚀 AutoAnalyze 快速启动脚本 (Windows)
echo    AI驱动的数据分析工具
echo ================================================================
echo.

:: 检查Python
echo 🔍 检查运行环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或不在PATH中
    echo 请安装Python 3.10+: https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    echo ✅ Python 已安装
)

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或不在PATH中
    echo 请安装Node.js 16+: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js 已安装
)

:: 检查npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm 未安装或不在PATH中
    pause
    exit /b 1
) else (
    echo ✅ npm 已安装
)

:: 检查uv
uv --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  uv 未安装，正在尝试安装...
    pip install uv
    if errorlevel 1 (
        echo ❌ uv 安装失败
        pause
        exit /b 1
    )
    echo ✅ uv 安装成功
) else (
    echo ✅ uv 已安装
)

echo.
echo 📦 安装项目依赖...

:: 安装后端依赖
echo   🔧 安装后端依赖...
cd backend
uv sync
if errorlevel 1 (
    echo   ❌ 后端依赖安装失败
    pause
    exit /b 1
)
echo   ✅ 后端依赖安装完成

:: 注册IPython内核
echo   🔧 注册IPython内核...
.venv\Scripts\python.exe -m ipykernel install --user >nul 2>&1
if errorlevel 1 (
    echo   ⚠️  IPython内核注册失败，部分功能可能不可用
) else (
    echo   ✅ IPython内核注册成功
)

:: 安装前端依赖
echo   🔧 安装前端依赖...
cd ..\frontend
npm install
if errorlevel 1 (
    echo   ❌ 前端依赖安装失败
    pause
    exit /b 1
)
echo   ✅ 前端依赖安装完成

cd ..
echo.

echo 🚀 启动服务...
echo   后端服务启动中...
echo   前端服务启动中...
echo.

:: 启动后端服务（在新窗口中）
start "AutoAnalyze Backend" cmd /k "cd backend && uv run app/main.py"

:: 等待2秒让后端启动
timeout /t 2 /nobreak >nul

:: 启动前端服务（在新窗口中）
start "AutoAnalyze Frontend" cmd /k "cd frontend && npm run dev"

:: 等待5秒让服务启动
echo ⏳ 等待服务启动完成...
timeout /t 5 /nobreak >nul

echo.
echo 🎉 AutoAnalyze 启动成功！
echo.
echo ================================================================
echo 📋 服务信息:
echo    前端地址: http://localhost:5173
echo    后端地址: http://127.0.0.1:8000
echo    
echo 💡 提示:
echo    - 前端和后端服务在独立窗口中运行
echo    - 关闭对应窗口即可停止服务
echo    - 如果浏览器未自动打开，请手动访问前端地址
echo ================================================================

:: 自动打开浏览器
echo 🌐 正在打开浏览器...
timeout /t 3 /nobreak >nul
start http://localhost:5173

echo.
echo 按任意键退出此窗口...
pause >nul
