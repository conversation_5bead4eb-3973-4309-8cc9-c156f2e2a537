# AutoAnalyze 快速启动指南

## 🚀 一键启动脚本

为了简化 AutoAnalyze 的启动流程，我们提供了多个启动脚本，让您可以一键完成环境检查、依赖安装和服务启动。

## 📁 启动脚本文件

| 文件名 | 适用平台 | 功能特点 |
|--------|----------|----------|
| `start_autoanalyze.py` | 跨平台 | 功能最完整，支持详细的环境检查和错误处理 |
| `start.bat` | Windows | 简单易用，在独立窗口中启动服务 |
| `start.sh` | Linux/macOS | 支持彩色输出，自动清理进程 |

## 🎯 推荐使用方式

### 方式一：Python脚本（推荐）

**适用于所有平台，功能最完整**

```bash
# 在 AutoAnalyze-master 目录下运行
python start_autoanalyze.py
```

**特点：**
- ✅ 自动环境检查（Python、Node.js、npm、uv）
- ✅ 自动安装缺失的依赖（如uv）
- ✅ 端口可用性检查
- ✅ 自动安装项目依赖
- ✅ 自动注册IPython内核
- ✅ 并行启动前后端服务
- ✅ 服务状态监控
- ✅ 自动打开浏览器
- ✅ 优雅的进程清理
- ✅ 彩色输出和详细日志

### 方式二：Windows批处理文件

**适用于Windows用户，简单快速**

```cmd
# 双击运行或在命令行中执行
start.bat
```

**特点：**
- ✅ 基础环境检查
- ✅ 自动安装依赖
- ✅ 在独立窗口中启动服务
- ✅ 自动打开浏览器
- ⚠️  需要手动关闭服务窗口

### 方式三：Linux/macOS Shell脚本

**适用于Linux和macOS用户**

```bash
# 首先给脚本执行权限
chmod +x start.sh

# 运行脚本
./start.sh
```

**特点：**
- ✅ 彩色输出界面
- ✅ 完整的环境检查
- ✅ 端口占用检查
- ✅ 自动进程管理
- ✅ Ctrl+C 优雅退出

## 🔧 使用前准备

### 必需软件

确保您的系统已安装以下软件：

1. **Python 3.10+**
   - Windows: https://www.python.org/downloads/
   - macOS: `brew install python` 或从官网下载
   - Linux: `sudo apt install python3` (Ubuntu) 或 `sudo yum install python3` (CentOS)

2. **Node.js 16+**
   - 官网下载: https://nodejs.org/
   - 或使用包管理器安装

3. **Git**（如果需要克隆项目）
   - 官网下载: https://git-scm.com/

### 可选软件

- **uv**: Python包管理器（脚本会自动安装）

## 📋 启动流程说明

所有启动脚本都会按以下顺序执行：

1. **环境检查** 🔍
   - 检查Python、Node.js、npm版本
   - 检查或安装uv包管理器
   - 验证端口可用性（8000、5173）

2. **依赖安装** 📦
   - 后端：`uv sync`（创建虚拟环境并安装Python依赖）
   - 前端：`npm install`（安装Node.js依赖）

3. **内核注册** 🔧
   - 注册IPython内核以支持Jupyter功能

4. **服务启动** 🚀
   - 后端服务：http://127.0.0.1:8000
   - 前端服务：http://localhost:5173

5. **浏览器打开** 🌐
   - 自动打开默认浏览器访问前端界面

## ⚠️ 常见问题解决

### 1. 权限问题（Windows）

如果遇到PowerShell执行策略限制：

```powershell
# 以管理员身份运行PowerShell，执行：
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 2. 端口占用

如果提示端口被占用：

```bash
# Windows - 查找占用端口的进程
netstat -ano | findstr :8000
netstat -ano | findstr :5173

# Linux/macOS - 查找占用端口的进程
lsof -i :8000
lsof -i :5173

# 终止进程（替换PID为实际进程ID）
kill -9 <PID>
```

### 3. 网络问题

如果依赖下载失败：

```bash
# 设置npm镜像源
npm config set registry https://registry.npmmirror.com/

# 设置pip镜像源
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 4. Python版本问题

如果系统有多个Python版本：

```bash
# 使用特定版本的Python运行脚本
python3.11 start_autoanalyze.py

# 或者创建虚拟环境
python -m venv autoanalyze_env
# Windows
autoanalyze_env\Scripts\activate
# Linux/macOS
source autoanalyze_env/bin/activate
```

## 🛠️ 高级配置

### 自定义端口

如需修改默认端口，请编辑以下文件：

1. **后端端口**：修改 `backend/config.yaml`
```yaml
server:
  port: 8080  # 修改为您需要的端口
```

2. **前端端口**：修改 `frontend/vite.config.ts`
```typescript
export default defineConfig({
  server: {
    port: 3000  // 修改为您需要的端口
  }
})
```

### 环境变量配置

可通过环境变量覆盖配置：

```bash
# 设置API密钥
export AUTOANALYZE_API_KEY="your-api-key"

# 设置自定义端口
export AUTOANALYZE_BACKEND_PORT="8080"
export AUTOANALYZE_FRONTEND_PORT="3000"
```

## 📊 服务状态检查

启动完成后，您可以通过以下方式检查服务状态：

1. **前端界面**：访问 http://localhost:5173
   - 正常情况下会显示："后端服务:连接成功 模型状态正常"

2. **后端API**：访问 http://127.0.0.1:8000/docs
   - 查看API文档和接口状态

3. **日志文件**：
   - 后端日志：`backend/app.log`
   - 前端日志：浏览器开发者工具控制台

## 🔄 停止服务

### Python脚本方式
- 按 `Ctrl+C` 即可优雅停止所有服务

### Windows批处理方式
- 关闭后端和前端的命令行窗口

### Linux/macOS脚本方式
- 按 `Ctrl+C` 即可优雅停止所有服务

## 🆘 获取帮助

如果遇到问题：

1. 📖 查看本文档的常见问题部分
2. 📋 检查 `backend/app.log` 日志文件
3. 🐛 在项目GitHub页面提交Issue
4. 💬 联系开发团队

## 🎉 开始使用

启动成功后：

1. 🌐 浏览器会自动打开 http://localhost:5173
2. 🔧 在设置页面配置您的AI模型API密钥
3. 📁 选择包含数据文件的工作目录
4. 💬 用自然语言描述您的数据分析需求
5. 🤖 观看AI为您自动分析数据！

---

**🚀 祝您使用愉快！开始您的AI数据分析之旅吧！**
