#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoAnalyze 快速启动脚本
支持自动环境检查、依赖安装、服务启动
"""

import os
import sys
import subprocess
import platform
import time
import webbrowser
import signal
import threading
from pathlib import Path

class Colors:
    """控制台颜色"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class AutoAnalyzeStarter:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.backend_process = None
        self.frontend_process = None
        self.is_windows = platform.system() == "Windows"
        
    def print_colored(self, message, color=Colors.WHITE):
        """打印彩色消息"""
        print(f"{color}{message}{Colors.END}")
        
    def print_header(self):
        """打印启动头部信息"""
        self.print_colored("=" * 60, Colors.CYAN)
        self.print_colored("🚀 AutoAnalyze 快速启动脚本", Colors.BOLD + Colors.GREEN)
        self.print_colored("   AI驱动的数据分析工具", Colors.CYAN)
        self.print_colored("=" * 60, Colors.CYAN)
        print()
        
    def check_command(self, command, version_flag="--version"):
        """检查命令是否存在并获取版本"""
        try:
            result = subprocess.run([command, version_flag],
                                  capture_output=True, text=True, timeout=10,
                                  shell=True if self.is_windows else False)
            if result.returncode == 0:
                output = result.stdout.strip() or result.stderr.strip()
                return True, output
            return False, ""
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            return False, ""
    
    def check_environment(self):
        """检查运行环境"""
        self.print_colored("🔍 检查运行环境...", Colors.YELLOW)
        
        # 检查Python
        python_ok, python_version = self.check_command("python", "--version")
        if python_ok:
            self.print_colored(f"✅ Python: {python_version}", Colors.GREEN)
        else:
            self.print_colored("❌ Python 未安装或不在PATH中", Colors.RED)
            return False
            
        # 检查Node.js
        node_ok, node_version = self.check_command("node", "--version")
        if node_ok:
            self.print_colored(f"✅ Node.js: {node_version}", Colors.GREEN)
        else:
            self.print_colored("❌ Node.js 未安装或不在PATH中", Colors.RED)
            return False
            
        # 检查npm
        npm_ok, npm_version = self.check_command("npm", "--version")
        if npm_ok:
            self.print_colored(f"✅ npm: {npm_version}", Colors.GREEN)
        else:
            self.print_colored("❌ npm 未安装或不在PATH中", Colors.RED)
            return False
            
        # 检查uv
        uv_ok, uv_version = self.check_command("uv", "--version")
        if uv_ok:
            self.print_colored(f"✅ uv: {uv_version}", Colors.GREEN)
        else:
            self.print_colored("⚠️  uv 未安装，正在尝试安装...", Colors.YELLOW)
            if not self.install_uv():
                return False
                
        print()
        return True
    
    def install_uv(self):
        """安装uv包管理器"""
        try:
            self.print_colored("📦 正在安装 uv...", Colors.YELLOW)
            subprocess.run([sys.executable, "-m", "pip", "install", "uv"], 
                         check=True, capture_output=True)
            self.print_colored("✅ uv 安装成功", Colors.GREEN)
            return True
        except subprocess.CalledProcessError:
            self.print_colored("❌ uv 安装失败，请手动安装: pip install uv", Colors.RED)
            return False
    
    def check_ports(self):
        """检查端口是否可用"""
        import socket
        
        def is_port_available(port):
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                try:
                    s.bind(('127.0.0.1', port))
                    return True
                except OSError:
                    return False
        
        self.print_colored("🔌 检查端口可用性...", Colors.YELLOW)
        
        if not is_port_available(8000):
            self.print_colored("❌ 端口 8000 已被占用（后端服务端口）", Colors.RED)
            return False
        else:
            self.print_colored("✅ 端口 8000 可用", Colors.GREEN)
            
        if not is_port_available(5173):
            self.print_colored("❌ 端口 5173 已被占用（前端服务端口）", Colors.RED)
            return False
        else:
            self.print_colored("✅ 端口 5173 可用", Colors.GREEN)
            
        print()
        return True
    
    def install_dependencies(self):
        """安装项目依赖"""
        self.print_colored("📦 安装项目依赖...", Colors.YELLOW)
        
        # 安装后端依赖
        self.print_colored("  🔧 安装后端依赖...", Colors.CYAN)
        try:
            os.chdir(self.backend_dir)
            subprocess.run(["uv", "sync"], check=True, shell=self.is_windows)
            self.print_colored("  ✅ 后端依赖安装完成", Colors.GREEN)
        except subprocess.CalledProcessError:
            self.print_colored("  ❌ 后端依赖安装失败", Colors.RED)
            return False

        # 安装前端依赖
        self.print_colored("  🔧 安装前端依赖...", Colors.CYAN)
        try:
            os.chdir(self.frontend_dir)
            subprocess.run(["npm", "install"], check=True, shell=self.is_windows)
            self.print_colored("  ✅ 前端依赖安装完成", Colors.GREEN)
        except subprocess.CalledProcessError:
            self.print_colored("  ❌ 前端依赖安装失败", Colors.RED)
            return False
        
        os.chdir(self.project_root)
        print()
        return True
    
    def register_ipython_kernel(self):
        """注册IPython内核"""
        self.print_colored("🔧 注册IPython内核...", Colors.YELLOW)
        try:
            os.chdir(self.backend_dir)
            if self.is_windows:
                # Windows
                subprocess.run([".venv\\Scripts\\python.exe", "-m", "ipykernel", "install", "--user"],
                             check=True, capture_output=True, shell=True)
            else:
                # Linux/macOS
                subprocess.run([".venv/bin/python", "-m", "ipykernel", "install", "--user"],
                             check=True, capture_output=True)
            self.print_colored("✅ IPython内核注册成功", Colors.GREEN)
            os.chdir(self.project_root)
            print()
            return True
        except subprocess.CalledProcessError:
            self.print_colored("❌ IPython内核注册失败", Colors.RED)
            os.chdir(self.project_root)
            return False
    
    def start_backend(self):
        """启动后端服务"""
        self.print_colored("🚀 启动后端服务...", Colors.YELLOW)
        try:
            os.chdir(self.backend_dir)
            # Windows和Linux/macOS使用不同的路径分隔符
            main_py_path = "app\\main.py" if self.is_windows else "app/main.py"

            if self.is_windows:
                # Windows: 在新的控制台窗口中启动
                cmd = f'start "AutoAnalyze Backend" cmd /k "cd /d {self.backend_dir} && uv run {main_py_path}"'
                self.backend_process = subprocess.Popen(cmd, shell=True)
                self.print_colored("  ✅ 后端服务窗口已启动", Colors.GREEN)
            else:
                # Linux/macOS: 在后台启动
                self.backend_process = subprocess.Popen(
                    ["uv", "run", main_py_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
            os.chdir(self.project_root)
            return True
        except Exception as e:
            self.print_colored(f"❌ 后端服务启动失败: {e}", Colors.RED)
            os.chdir(self.project_root)
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        self.print_colored("🚀 启动前端服务...", Colors.YELLOW)
        try:
            os.chdir(self.frontend_dir)

            if self.is_windows:
                # Windows: 在新的控制台窗口中启动
                cmd = f'start "AutoAnalyze Frontend" cmd /k "cd /d {self.frontend_dir} && npm run dev"'
                self.frontend_process = subprocess.Popen(cmd, shell=True)
                self.print_colored("  ✅ 前端服务窗口已启动", Colors.GREEN)
            else:
                # Linux/macOS: 在后台启动
                self.frontend_process = subprocess.Popen(
                    ["npm", "run", "dev"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
            os.chdir(self.project_root)
            return True
        except Exception as e:
            self.print_colored(f"❌ 前端服务启动失败: {e}", Colors.RED)
            os.chdir(self.project_root)
            return False
    
    def wait_for_services(self):
        """等待服务启动完成"""
        self.print_colored("⏳ 等待服务启动完成...", Colors.YELLOW)

        if self.is_windows:
            # Windows: 给服务更多时间启动，因为是在独立窗口中
            self.print_colored("  💡 服务正在独立窗口中启动，请稍等...", Colors.CYAN)
            time.sleep(8)  # 给服务足够的启动时间

        # 等待后端服务
        backend_ready = False
        self.print_colored("  🔍 检查后端服务状态...", Colors.CYAN)
        for i in range(60):  # 最多等待60秒
            try:
                import urllib.request
                urllib.request.urlopen("http://127.0.0.1:8000", timeout=2)
                backend_ready = True
                break
            except:
                if i % 5 == 0:  # 每5秒显示一次进度
                    self.print_colored(f"    等待后端服务... ({i+1}/60秒)", Colors.YELLOW)
                time.sleep(1)

        if backend_ready:
            self.print_colored("✅ 后端服务启动成功 (http://127.0.0.1:8000)", Colors.GREEN)
        else:
            self.print_colored("⚠️  后端服务启动超时，但可能仍在启动中", Colors.YELLOW)

        # 等待前端服务
        frontend_ready = False
        self.print_colored("  🔍 检查前端服务状态...", Colors.CYAN)
        for i in range(60):  # 最多等待60秒
            try:
                import urllib.request
                urllib.request.urlopen("http://localhost:5173", timeout=2)
                frontend_ready = True
                break
            except:
                if i % 5 == 0:  # 每5秒显示一次进度
                    self.print_colored(f"    等待前端服务... ({i+1}/60秒)", Colors.YELLOW)
                time.sleep(1)

        if frontend_ready:
            self.print_colored("✅ 前端服务启动成功 (http://localhost:5173)", Colors.GREEN)
        else:
            self.print_colored("⚠️  前端服务启动超时，但可能仍在启动中", Colors.YELLOW)

        # 在Windows下，即使检测超时也认为启动成功，因为服务在独立窗口中运行
        if self.is_windows:
            return True
        else:
            return backend_ready and frontend_ready
    
    def open_browser(self):
        """打开浏览器"""
        self.print_colored("🌐 正在打开浏览器...", Colors.CYAN)
        try:
            webbrowser.open("http://localhost:5173")
            self.print_colored("✅ 浏览器已打开", Colors.GREEN)
        except Exception as e:
            self.print_colored(f"⚠️  无法自动打开浏览器: {e}", Colors.YELLOW)
            self.print_colored("请手动访问: http://localhost:5173", Colors.CYAN)
    
    def cleanup(self):
        """清理进程"""
        self.print_colored("\n🛑 正在关闭服务...", Colors.YELLOW)

        if self.is_windows:
            # Windows: 提供手动关闭指导
            self.print_colored("💡 Windows环境下，服务运行在独立窗口中", Colors.CYAN)
            self.print_colored("   请手动关闭以下窗口来停止服务：", Colors.CYAN)
            self.print_colored("   - AutoAnalyze Backend (后端服务窗口)", Colors.CYAN)
            self.print_colored("   - AutoAnalyze Frontend (前端服务窗口)", Colors.CYAN)
            self.print_colored("   或者按 Ctrl+C 在各个服务窗口中停止服务", Colors.CYAN)
        else:
            # Linux/macOS: 正常终止进程
            if self.backend_process:
                self.backend_process.terminate()
                self.print_colored("✅ 后端服务已关闭", Colors.GREEN)

            if self.frontend_process:
                self.frontend_process.terminate()
                self.print_colored("✅ 前端服务已关闭", Colors.GREEN)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """主运行函数"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        self.print_header()
        
        # 检查环境
        if not self.check_environment():
            self.print_colored("❌ 环境检查失败，请安装必需的软件", Colors.RED)
            return False
        
        # 检查端口
        if not self.check_ports():
            self.print_colored("❌ 端口检查失败，请关闭占用端口的程序", Colors.RED)
            return False
        
        # 安装依赖
        if not self.install_dependencies():
            self.print_colored("❌ 依赖安装失败", Colors.RED)
            return False
        
        # 注册内核
        if not self.register_ipython_kernel():
            self.print_colored("⚠️  内核注册失败，部分功能可能不可用", Colors.YELLOW)
        
        # 启动服务
        if not self.start_backend():
            self.print_colored("❌ 后端服务启动失败", Colors.RED)
            return False
            
        if not self.start_frontend():
            self.print_colored("❌ 前端服务启动失败", Colors.RED)
            self.cleanup()
            return False
        
        # 等待服务启动
        if self.wait_for_services():
            self.print_colored("\n🎉 AutoAnalyze 启动成功！", Colors.BOLD + Colors.GREEN)
            self.open_browser()

            self.print_colored("\n" + "=" * 60, Colors.CYAN)
            self.print_colored("📋 服务信息:", Colors.BOLD)
            self.print_colored("   前端地址: http://localhost:5173", Colors.CYAN)
            self.print_colored("   后端地址: http://127.0.0.1:8000", Colors.CYAN)

            if self.is_windows:
                self.print_colored("\n💡 Windows使用提示:", Colors.BOLD)
                self.print_colored("   - 前后端服务运行在独立窗口中", Colors.CYAN)
                self.print_colored("   - 关闭服务窗口即可停止对应服务", Colors.CYAN)
                self.print_colored("   - 按任意键退出此启动脚本", Colors.YELLOW)
                self.print_colored("=" * 60, Colors.CYAN)

                # Windows下不需要保持脚本运行
                input("\n按回车键退出启动脚本...")
            else:
                self.print_colored("   按 Ctrl+C 停止服务", Colors.YELLOW)
                self.print_colored("=" * 60, Colors.CYAN)

                # Linux/macOS下保持运行
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    pass
        else:
            self.print_colored("❌ 服务启动失败", Colors.RED)
            self.cleanup()
            return False
        
        self.cleanup()
        return True

if __name__ == "__main__":
    starter = AutoAnalyzeStarter()
    success = starter.run()
    sys.exit(0 if success else 1)
