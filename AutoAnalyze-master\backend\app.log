2025-06-08 16:45:14,562 - app.api.status - INFO - tag被设置为了aba6f161-8062-43de-b7c8-d0a6c1d569e3
2025-06-08 16:45:14,563 - app.main - INFO - 应用启动完成
2025-06-08 16:47:00,842 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F91328050>
2025-06-08 16:47:00,842 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91330050>, 9474.6727044)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F913281A0>
2025-06-08 16:47:08,389 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EB4D0>
2025-06-08 16:47:08,390 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F9132C050>, 9482.2198156)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F912EB610>
2025-06-08 16:53:27,277 - app.config - INFO - 保存配置到文件: D:\新建文件夹\人工智能技术应用\人工智能\AutoAnalyze-master\backend\config.yaml
2025-06-08 16:53:27,823 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EAD50>
2025-06-08 16:53:27,824 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91338050>, 9861.6544573)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F912EB110>
2025-06-08 16:53:43,014 - app.config - INFO - 保存配置到文件: D:\新建文件夹\人工智能技术应用\人工智能\AutoAnalyze-master\backend\config.yaml
2025-06-08 16:53:43,873 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EAAD0>
2025-06-08 16:53:43,874 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91340050>, 9877.2195867)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026F950>
2025-06-08 16:53:43,875 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EA210>
2025-06-08 16:53:43,876 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F913402F0>, 9877.7040098)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F913383E0>
2025-06-08 16:53:48,668 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EA0D0>
2025-06-08 16:53:48,669 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F913402F0>, 9882.0074676)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026F360>
2025-06-08 16:53:48,670 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EA5D0>
2025-06-08 16:53:48,671 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91340050>, 9882.4989109)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026FE10>
2025-06-08 16:54:02,059 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912E9BD0>
2025-06-08 16:54:02,060 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91340050>, 9895.4469689)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026F5C0>
2025-06-08 16:54:02,060 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EA5D0>
2025-06-08 16:54:02,061 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F913402F0>, 9895.88966)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026F6F0>
2025-06-08 16:54:11,448 - app.config - INFO - 保存配置到文件: D:\新建文件夹\人工智能技术应用\人工智能\AutoAnalyze-master\backend\config.yaml
2025-06-08 16:54:11,770 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EA210>
2025-06-08 16:54:11,771 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91340050>, 9905.6009089)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026EC40>
2025-06-08 16:54:13,952 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EA0D0>
2025-06-08 16:54:13,953 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91338050>, 9907.7831234)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026EC40>
2025-06-08 16:54:16,860 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EA5D0>
2025-06-08 16:54:16,861 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F91340050>, 9910.6911764)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026EC40>
2025-06-08 16:59:46,993 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000024F912EB4D0>
2025-06-08 16:59:46,994 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000024F913380B0>, 10240.8236743)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000024F9026EC40>
2025-06-11 17:25:59,445 - app.api.status - INFO - tag被设置为了3437e11d-f926-4d0c-b6bf-6d868c8ea989
2025-06-11 17:25:59,446 - app.main - INFO - 应用启动完成
2025-06-11 17:26:47,463 - app.core.model_client - INFO - 当前检查的服务可用的模型共有324个。
2025-06-11 17:26:47,464 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002627033FE00>
2025-06-11 17:26:47,464 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000262703B4410>, 3530.232299)])']
connector: <aiohttp.connector.TCPConnector object at 0x00000262703CC050>
2025-06-11 17:27:36,896 - app.core.model_client - INFO - 当前检查的服务可用的模型共有324个。
2025-06-11 17:27:36,896 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002627037AC10>
2025-06-11 17:27:36,897 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000262703B4410>, 3579.6648481)])']
connector: <aiohttp.connector.TCPConnector object at 0x000002627037AD50>
2025-06-11 17:28:33,458 - app.api.workspace - INFO - 前端选择工作目录为: D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计
2025-06-11 17:28:33,459 - app.core.filesystem - INFO - 设置工作目录: D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计
2025-06-11 17:28:36,519 - app.services.filesystem_service - INFO - 尝试读取D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计\2024年5月份各学院学生图书借阅量统计.xlsx
2025-06-11 17:29:09,942 - app.services.model_service - INFO - 接收到用户消息:给出我们的表的分析

2025-06-11 17:29:11,156 - app.core.model_client - ERROR - OpenAI API错误: 404 - {"error":{"message":"No endpoints found that support tool use. To learn more about provider routing, visit: https://openrouter.ai/docs/provider-routing","code":404}}
2025-06-11 17:29:23,809 - app.services.filesystem_service - INFO - 尝试读取D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计\2024年5月份各学院学生图书借阅量统计.xlsx
2025-06-11 17:29:28,222 - app.services.filesystem_service - INFO - 尝试读取D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计\2024年5月份学生读者借阅统计.xls
2025-06-11 17:29:41,681 - app.services.filesystem_service - INFO - 尝试读取D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计\2024年5月份各学院学生图书借阅量统计.xlsx
2025-06-11 17:29:47,159 - app.services.filesystem_service - INFO - 尝试读取D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计\2024年5月份学生读者借阅统计.xls
2025-06-11 17:30:10,683 - app.websocket.router - INFO - WebSocket客户端断开连接
2025-06-11 17:30:10,915 - app.core.model_client - INFO - 当前检查的服务可用的模型共有324个。
2025-06-11 17:30:10,916 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002627037AD50>
2025-06-11 17:30:10,917 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000262707F8E90>, 3733.6849269)])']
connector: <aiohttp.connector.TCPConnector object at 0x00000262703D6520>
2025-06-11 17:30:27,870 - app.api.workspace - INFO - 前端选择工作目录为: D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计
2025-06-11 17:30:27,871 - app.core.filesystem - INFO - 设置工作目录: D:\新建文件夹\学生会\图书借阅量\2024年5月份各学院学生读书量统计
2025-06-11 17:30:40,151 - app.services.model_service - INFO - 接收到用户消息:给出我们的统计分析图

2025-06-11 17:30:41,340 - app.core.model_client - ERROR - OpenAI API错误: 404 - {"error":{"message":"No endpoints found that support tool use. To learn more about provider routing, visit: https://openrouter.ai/docs/provider-routing","code":404}}
2025-06-11 17:31:03,181 - app.core.model_client - INFO - 当前检查的服务可用的模型共有324个。
2025-06-11 17:31:03,182 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000262703D3110>
2025-06-11 17:31:03,183 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000262707F8A70>, 3785.9502683)])']
connector: <aiohttp.connector.TCPConnector object at 0x00000262703D5350>
2025-06-11 17:31:08,658 - app.core.model_client - INFO - 当前检查的服务可用的模型共有324个。
2025-06-11 17:31:08,660 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x00000262703D1950>
2025-06-11 17:31:08,660 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x00000262707F8BF0>, 3791.4255459)])']
connector: <aiohttp.connector.TCPConnector object at 0x00000262703D5350>
2025-06-11 17:32:44,392 - app.websocket.router - INFO - WebSocket客户端断开连接
2025-06-11 17:33:56,838 - app.api.status - INFO - tag被设置为了950228ec-79cc-4516-8cbf-9ebfe4016723
2025-06-11 17:33:56,839 - app.main - INFO - 应用启动完成
2025-06-16 12:27:41,724 - app.api.status - INFO - tag被设置为了91968004-5952-4cf3-9f35-e4ab49cc46de
2025-06-16 12:27:41,726 - app.main - INFO - 应用启动完成
2025-06-16 12:29:41,616 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000021CAB769610>
2025-06-16 12:29:41,617 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000021CADAD6ED0>, 1409.406)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000021CAD20CEC0>
2025-06-16 12:29:53,497 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000021CAD830470>
2025-06-16 12:29:53,497 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000021CADAD7170>, 1421.296)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000021CAD831C70>
2025-06-16 12:30:02,713 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000021CAD831730>
2025-06-16 12:30:02,713 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000021CADAD6ED0>, 1430.515)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000021CAD832930>
2025-06-16 12:30:05,687 - app.config - INFO - 保存配置到文件: D:\新建文件夹\人工智能技术应用\人工智能\AutoAnalyze-master\backend\config.yaml
2025-06-16 12:30:05,972 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000021CAD74E510>
2025-06-16 12:30:05,973 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000021CADAD71D0>, 1433.765)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000021CAD344320>
2025-06-16 12:30:11,265 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000021CAD833380>
2025-06-16 12:30:11,266 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000021CADAD71D0>, 1439.062)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000021CAD833290>
2025-06-16 12:32:09,171 - app.api.workspace - INFO - 前端选择工作目录为: D:\?????\????????\????\AutoAnalyze-master\test_data
2025-06-16 12:32:23,507 - app.api.workspace - INFO - 前端选择工作目录为: D:\?????\????????\????\AutoAnalyze-master\frontend\test_data
2025-06-16 12:32:52,637 - app.api.workspace - INFO - 前端选择工作目录为: D:\?????\????????\????\AutoAnalyze-master\test_data
2025-06-16 12:33:09,479 - app.api.workspace - INFO - 前端选择工作目录为: D:\?????\????????\????\AutoAnalyze-master\examples
2025-08-01 11:03:37,568 - app.api.status - INFO - tag被设置为了8e60556d-cbae-4b4b-bf88-448b3ba8cd6a
2025-08-01 11:03:37,569 - app.main - INFO - 应用启动完成
2025-08-01 11:04:56,741 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000027DC5C20D70>
2025-08-01 11:04:56,742 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000027DC5BE3BF0>, 3364.5)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000027DC5C20C20>
2025-08-01 11:05:05,176 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000027DC5C20D40>
2025-08-01 11:05:05,176 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000027DC5BE3F50>, 3372.937)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000027DC5C21CD0>
2025-08-01 11:05:16,589 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x0000027DC5C225D0>
2025-08-01 11:05:16,590 - asyncio - ERROR - Unclosed connector
connections: ['deque([(<aiohttp.client_proto.ResponseHandler object at 0x0000027DC5BE3FB0>, 3384.343)])']
connector: <aiohttp.connector.TCPConnector object at 0x0000027DC5C222D0>
2025-08-01 11:08:09,458 - app.api.status - INFO - tag被设置为了7bb66c85-fa04-4634-817d-92c876e6eb43
2025-08-01 11:08:09,458 - app.main - INFO - 应用启动完成
2025-08-01 11:09:50,186 - app.api.status - INFO - tag被设置为了4f13aca4-dd2e-42ac-8b9f-9ef49bd08af9
2025-08-01 11:09:50,186 - app.main - INFO - 应用启动完成
2025-08-01 11:37:37,232 - app.api.status - INFO - tag被设置为了e4a5a5b6-1a08-40ff-9c54-44fe08c64c0d
2025-08-01 11:37:37,233 - app.main - INFO - 应用启动完成
2025-08-01 11:37:37,233 - app.main - INFO - 应用关闭
2025-08-01 11:40:39,240 - app.core.model_client - ERROR - 测试OpenAI连接时出错: 'ok'
2025-08-01 11:40:44,146 - app.core.model_client - ERROR - 测试OpenAI连接时出错: 'ok'
2025-08-01 11:41:11,368 - app.core.model_client - ERROR - 测试OpenAI连接时出错: 'ok'
2025-08-01 11:45:03,812 - app.core.model_client - ERROR - 测试OpenAI连接时出错: 'ok'
2025-08-01 11:45:34,915 - app.core.model_client - ERROR - 测试OpenAI连接时出错: 'ok'
2025-08-01 12:01:23,929 - app.api.status - INFO - tag被设置为了02794569-09f7-4862-8af9-37b29afa1b58
2025-08-01 12:01:23,929 - app.main - INFO - 应用启动完成
2025-08-01 15:03:19,658 - app.api.status - INFO - tag被设置为了b0eba13c-563b-44e1-9f97-a1aabd403022
2025-08-01 15:03:19,659 - app.main - INFO - 应用启动完成
2025-08-01 15:03:38,777 - app.main - INFO - 应用关闭
2025-08-01 15:05:11,579 - app.api.status - INFO - tag被设置为了05a95931-5e91-4edb-824f-8753c97b3359
2025-08-01 15:05:11,581 - app.main - INFO - 应用启动完成
